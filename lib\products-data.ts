type TranslationFunction = (key: string) => string;

// Function to get products with translations - Main products only
export const getProducts = (t: TranslationFunction) => [
  {
    slug: "ai-annotation",
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    iconName: "Brain",
    features: [
      { text: t("products.aiAnnotation.features.imageAnnotation"), iconName: "Eye" },
      { text: t("products.aiAnnotation.features.textAnnotation"), iconName: "Database" },
      { text: t("products.aiAnnotation.features.audioAnnotation"), iconName: "Target" },
      { text: t("products.aiAnnotation.features.qualityControl"), iconName: "CheckCircle2" }
    ],
    highlight: t("products.aiAnnotation.highlight"),
    price: t("products.aiAnnotation.price"),
    category: t("products.aiAnnotation.category")
  },
  {
    slug: "cpu-rental",
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    iconName: "Cpu",
    features: [
      { text: t("products.cpuRental.features.highPerformance"), iconName: "Zap" },
      { text: t("products.cpuRental.features.elasticScaling"), iconName: "Globe" },
      { text: t("products.cpuRental.features.payAsYouGo"), iconName: "BarChart" },
      { text: t("products.cpuRental.features.monitoring247"), iconName: "Shield" }
    ],
    highlight: t("products.cpuRental.highlight"),
    price: t("products.cpuRental.price"),
    category: t("products.cpuRental.category")
  },
  {
    slug: "education-management",
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    iconName: "GraduationCap",
    features: [
      { text: t("products.educationManagement.features.courseManagement"), iconName: "BookOpen" },
      { text: t("products.educationManagement.features.onlineExam"), iconName: "Target" },
      { text: t("products.educationManagement.features.studentTracking"), iconName: "Users" },
      { text: t("products.educationManagement.features.certificateSystem"), iconName: "Award" }
    ],
    highlight: t("products.educationManagement.highlight"),
    price: t("products.educationManagement.price"),
    category: t("products.educationManagement.category")
  },
  {
    slug: "data-annotation-pro",
    name: t("products.dataAnnotationPro.name"),
    description: t("products.dataAnnotationPro.description"),
    iconName: "Target",
    features: [
      { text: t("products.dataAnnotationPro.features.customSolution"), iconName: "Settings" },
      { text: t("products.dataAnnotationPro.features.professionalQC"), iconName: "CheckCircle2" },
      { text: t("products.dataAnnotationPro.features.batchProcessing"), iconName: "Database" },
      { text: t("products.dataAnnotationPro.features.apiIntegration"), iconName: "Globe" }
    ],
    highlight: t("products.dataAnnotationPro.highlight"),
    price: t("products.dataAnnotationPro.price"),
    category: t("products.dataAnnotationPro.category")
  },
  {
    slug: "custom-web-development",
    name: t("products.customWebDevelopment.name"),
    description: t("products.customWebDevelopment.description"),
    iconName: "Globe",
    features: [
      { text: t("products.customWebDevelopment.features.responsiveDesign"), iconName: "Eye" },
      { text: t("products.customWebDevelopment.features.modernFrameworks"), iconName: "Zap" },
      { text: t("products.customWebDevelopment.features.seoOptimized"), iconName: "TrendingUp" },
      { text: t("products.customWebDevelopment.features.performanceOptimized"), iconName: "Shield" }
    ],
    highlight: t("products.customWebDevelopment.highlight"),
    price: t("products.customWebDevelopment.price"),
    category: t("products.customWebDevelopment.category")
  },
  {
    slug: "mobile-app-development",
    name: t("products.mobileAppDevelopment.name"),
    description: t("products.mobileAppDevelopment.description"),
    iconName: "Smartphone",
    features: [
      { text: t("products.mobileAppDevelopment.features.crossPlatform"), iconName: "Globe" },
      { text: t("products.mobileAppDevelopment.features.nativePerformance"), iconName: "Zap" },
      { text: t("products.mobileAppDevelopment.features.uiuxDesign"), iconName: "Eye" },
      { text: t("products.mobileAppDevelopment.features.appStoreOptimized"), iconName: "Award" }
    ],
    highlight: t("products.mobileAppDevelopment.highlight"),
    price: t("products.mobileAppDevelopment.price"),
    category: t("products.mobileAppDevelopment.category")
  },
  {
    slug: "enterprise-software",
    name: t("products.enterpriseSoftware.name"),
    description: t("products.enterpriseSoftware.description"),
    iconName: "Building",
    features: [
      { text: t("products.enterpriseSoftware.features.scalableArchitecture"), iconName: "BarChart" },
      { text: t("products.enterpriseSoftware.features.securityCompliance"), iconName: "Shield" },
      { text: t("products.enterpriseSoftware.features.integrationSupport"), iconName: "Globe" },
      { text: t("products.enterpriseSoftware.features.customWorkflows"), iconName: "Settings" }
    ],
    highlight: t("products.enterpriseSoftware.highlight"),
    price: t("products.enterpriseSoftware.price"),
    category: t("products.enterpriseSoftware.category")
  }
];

// Function to get product details with translations
export const getProductDetails = (t: TranslationFunction) => ({
  "ai-annotation": {
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    features: [
      t("products.aiAnnotation.features.imageAnnotation"),
      t("products.aiAnnotation.features.textAnnotation"),
      t("products.aiAnnotation.features.audioAnnotation"),
      t("products.aiAnnotation.features.qualityControl")
    ],
    techSpecs: {
      deployment: "云端部署",
      security: "企业级安全",
      availability: "99.9%可用性",
      support: "24/7技术支持"
    }
  },
  "cpu-rental": {
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    features: [
      t("products.cpuRental.features.highPerformance"),
      t("products.cpuRental.features.elasticScaling"),
      t("products.cpuRental.features.payAsYouGo"),
      t("products.cpuRental.features.monitoring247")
    ],
    techSpecs: {
      deployment: "多地区部署",
      security: "数据加密",
      availability: "99.9%可用性",
      support: "专业技术支持"
    }
  },
  "education-management": {
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    features: [
      t("products.educationManagement.features.courseManagement"),
      t("products.educationManagement.features.onlineExam"),
      t("products.educationManagement.features.studentTracking"),
      t("products.educationManagement.features.certificateSystem")
    ],
    techSpecs: {
      deployment: "私有云/公有云",
      security: "数据安全保护",
      availability: "高可用架构",
      support: "专业培训支持"
    }
  }
});

// Legacy exports for backward compatibility
export const products = [];
export const productDetails = {};
