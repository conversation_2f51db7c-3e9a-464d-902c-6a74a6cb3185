"use client"

import { ProductCard } from "./ProductCard"
import { ProductHero } from "./ProductHero"
import { ProductCategories } from "./ProductCategories"
import { ProductFeatures } from "./ProductFeatures"
import { ProductStats } from "./ProductStats"
import { ProductCTA } from "./ProductCTA"
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

interface ProductsClientProps {
  products: Product[]
}

export function ProductsClient({ products }: ProductsClientProps) {
  const t = useTranslations('productsPage')
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')

  useEffect(() => {
    let filtered = products

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // 按搜索关键词筛选
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredProducts(filtered)
  }, [selectedCategory, searchQuery, products])

  const categories = [
    { id: 'all', name: t('categories.all'), count: products.length },
    { id: 'AI服务', name: t('categories.ai'), count: products.filter(p => p.category === 'AI服务').length },
    { id: '云计算', name: t('categories.cloud'), count: products.filter(p => p.category === '云计算').length },
    { id: '教育科技', name: t('categories.education'), count: products.filter(p => p.category === '教育科技').length },
    { id: '定制开发', name: t('categories.development'), count: products.filter(p => p.category === '定制开发').length },
  ]

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <ProductHero
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {/* Product Categories */}
      <ProductCategories
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
      />

      {/* Enhanced Product Grid */}
      <section className="py-20 sm:py-24 bg-gradient-to-br from-slate-50/20 via-white to-blue-50/10 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-40 left-20 w-40 h-40 bg-blue-100/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-40 right-20 w-32 h-32 bg-indigo-100/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          {/* Enhanced Section Header */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100/50 backdrop-blur-sm mb-6 shadow-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-3 h-3 rounded-full bg-blue-500"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <span className="text-sm font-semibold text-blue-700">
                {selectedCategory === 'all' ? '全部产品展示' : `${categories.find(c => c.id === selectedCategory)?.name} 产品展示`}
              </span>
            </motion.div>
            <h3 className="text-3xl sm:text-4xl font-bold text-slate-900 mb-4 bg-gradient-to-r from-slate-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent">
              发现适合您的解决方案
            </h3>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              共找到 <span className="font-bold text-blue-600 text-xl">{filteredProducts.length}</span> 个优质产品，为您的业务提供专业技术支持
            </p>
          </motion.div>

          {filteredProducts.length === 0 ? (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                <div className="text-4xl">🔍</div>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2">{t('noProductsFound')}</h3>
              <p className="text-slate-600 mb-6">{t('tryDifferentSearch')}</p>
              <motion.button
                className="px-6 py-3 bg-blue-500 text-white rounded-xl font-medium hover:bg-blue-600 transition-colors"
                onClick={() => {
                  setSelectedCategory('all')
                  setSearchQuery('')
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                查看全部产品
              </motion.button>
            </motion.div>
          ) : (
            <motion.div
              className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {filteredProducts.map((product, index) => (
                <motion.div
                  key={product.slug}
                  initial={{ opacity: 0, y: 40, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.6,
                    delay: index * 0.15,
                    type: "spring",
                    stiffness: 120,
                    damping: 12
                  }}
                  className="h-full"
                >
                  <ProductCard product={product} index={index} />
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </section>

      {/* Product Features */}
      <ProductFeatures />

      {/* Product Stats */}
      <ProductStats />

      {/* CTA Section */}
      <ProductCTA />
    </div>
  )
}
