"use client"

import { ProductCard } from "./ProductCard"
import { ProductHero } from "./ProductHero"
import { ProductCategories } from "./ProductCategories"
import { ProductFeatures } from "./ProductFeatures"
import { ProductStats } from "./ProductStats"
import { ProductCTA } from "./ProductCTA"
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

interface ProductsClientProps {
  products: Product[]
}

export function ProductsClient({ products }: ProductsClientProps) {
  const t = useTranslations('productsPage')
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')

  useEffect(() => {
    let filtered = products

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // 按搜索关键词筛选
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredProducts(filtered)
  }, [selectedCategory, searchQuery, products])

  const categories = [
    { id: 'all', name: t('categories.all'), count: products.length },
    { id: 'AI服务', name: t('categories.ai'), count: products.filter(p => p.category === 'AI服务').length },
    { id: '云计算', name: t('categories.cloud'), count: products.filter(p => p.category === '云计算').length },
    { id: '教育科技', name: t('categories.education'), count: products.filter(p => p.category === '教育科技').length },
    { id: '定制开发', name: t('categories.development'), count: products.filter(p => p.category === '定制开发').length },
  ]

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <ProductHero
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {/* Product Categories */}
      <ProductCategories
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
      />

      {/* Product Grid */}
      <section className="py-16 sm:py-20 bg-gradient-to-br from-slate-50/30 via-white to-blue-50/20">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          {/* Section Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-50 border border-blue-100 mb-4">
              <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse" />
              <span className="text-sm font-medium text-blue-700">
                {selectedCategory === 'all' ? '全部产品' : `${categories.find(c => c.id === selectedCategory)?.name} 产品`}
              </span>
            </div>
            <h3 className="text-2xl font-bold text-slate-900 mb-2">
              发现适合您的解决方案
            </h3>
            <p className="text-slate-600 max-w-2xl mx-auto">
              共找到 <span className="font-semibold text-blue-600">{filteredProducts.length}</span> 个产品
            </p>
          </motion.div>

          {filteredProducts.length === 0 ? (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                <div className="text-4xl">🔍</div>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2">{t('noProductsFound')}</h3>
              <p className="text-slate-600 mb-6">{t('tryDifferentSearch')}</p>
              <motion.button
                className="px-6 py-3 bg-blue-500 text-white rounded-xl font-medium hover:bg-blue-600 transition-colors"
                onClick={() => {
                  setSelectedCategory('all')
                  setSearchQuery('')
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                查看全部产品
              </motion.button>
            </motion.div>
          ) : (
            <motion.div
              className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {filteredProducts.map((product, index) => (
                <motion.div
                  key={product.slug}
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100
                  }}
                  whileHover={{ y: -5 }}
                  className="h-full"
                >
                  <ProductCard product={product} index={index} />
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </section>

      {/* Product Features */}
      <ProductFeatures />

      {/* Product Stats */}
      <ProductStats />

      {/* CTA Section */}
      <ProductCTA />
    </div>
  )
}
