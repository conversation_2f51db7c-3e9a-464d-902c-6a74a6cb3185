"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import {
  ArrowRight,
  Star,
  TrendingUp,
  Users,
  Shield,
  Zap,
  Sparkles,
  ChevronRight,
  Clock,
  CheckCircle,
  Heart,
  Bookmark,
  Share2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useMemo, useCallback } from "react"
import { useTranslations } from '@/hooks/useTranslations'
import {
  Brain,
  Cpu,
  GraduationCap,
  Database,
  Eye,
  Target,
  CheckCircle2,
  Globe,
  BarChart,
  BookOpen,
  Award,
  Settings,
  Smartphone,
  Building,
  type LucideIcon
} from "lucide-react"




interface ProductFeature {
  text: string
  iconName: string
}

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: ProductFeature[]
  highlight?: string
  price?: string
  category?: string
}

interface ProductCardProps {
  product: Product
  index: number
}

// 图标映射
const ICON_MAP: Record<string, LucideIcon> = {
  Brain: Brain,
  Cpu: Cpu,
  GraduationCap: GraduationCap,
  Database: Database,
  Eye: Eye,
  Target: Target,
  CheckCircle2: CheckCircle2,
  Globe: Globe,
  BarChart: BarChart,
  BookOpen: BookOpen,
  Award: Award,
  Settings: Settings,
  Zap: Zap,
  Shield: Shield,
  Users: Users,
  Smartphone: Smartphone,
  Building: Building,
  TrendingUp: TrendingUp
}

// 渲染图标函数
const renderIcon = (iconName: string, className?: string) => {
  const IconComponent = ICON_MAP[iconName]
  if (!IconComponent) {
    return <div className={cn("bg-blue-500/20 rounded", className)} />
  }
  return <IconComponent className={className} />
}

export function ProductCard({ product, index }: ProductCardProps) {
  const t = useTranslations('productsPage')
  const tHighlights = useTranslations('productsPage.highlights')
  const tCard = useTranslations('productsPage.card')
  const [isHovered, setIsHovered] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false)
  const [isLikeLoading, setIsLikeLoading] = useState(false)

  // 增强的现代化主题色彩系统 - 与ProductCategories保持一致
  const getThemeColors = (category: string) => {
    switch (category) {
      case 'AI服务':
        return {
          primary: 'from-blue-500 via-blue-600 to-indigo-600',
          secondary: 'from-blue-50/80 via-indigo-50/60 to-purple-50/40',
          accent: '#3B82F6',
          accentRgb: '59, 130, 246',
          light: 'rgba(59, 130, 246, 0.1)',
          dark: 'rgba(59, 130, 246, 0.9)',
          glow: '0 0 30px rgba(59, 130, 246, 0.3)',
          cardBg: 'from-blue-50/90 via-indigo-50/70 to-white/80'
        }
      case '云计算':
        return {
          primary: 'from-emerald-500 via-green-600 to-teal-600',
          secondary: 'from-emerald-50/80 via-green-50/60 to-teal-50/40',
          accent: '#10B981',
          accentRgb: '16, 185, 129',
          light: 'rgba(16, 185, 129, 0.1)',
          dark: 'rgba(16, 185, 129, 0.9)',
          glow: '0 0 30px rgba(16, 185, 129, 0.3)',
          cardBg: 'from-emerald-50/90 via-green-50/70 to-white/80'
        }
      case '教育科技':
        return {
          primary: 'from-purple-500 via-violet-600 to-fuchsia-600',
          secondary: 'from-purple-50/80 via-violet-50/60 to-fuchsia-50/40',
          accent: '#8B5CF6',
          accentRgb: '139, 92, 246',
          light: 'rgba(139, 92, 246, 0.1)',
          dark: 'rgba(139, 92, 246, 0.9)',
          glow: '0 0 30px rgba(139, 92, 246, 0.3)',
          cardBg: 'from-purple-50/90 via-violet-50/70 to-white/80'
        }
      case '定制开发':
        return {
          primary: 'from-orange-500 via-red-600 to-pink-600',
          secondary: 'from-orange-50/80 via-red-50/60 to-pink-50/40',
          accent: '#F97316',
          accentRgb: '249, 115, 22',
          light: 'rgba(249, 115, 22, 0.1)',
          dark: 'rgba(249, 115, 22, 0.9)',
          glow: '0 0 30px rgba(249, 115, 22, 0.3)',
          cardBg: 'from-orange-50/90 via-red-50/70 to-white/80'
        }
      default:
        return {
          primary: 'from-slate-500 via-gray-600 to-zinc-600',
          secondary: 'from-slate-50/80 via-gray-50/60 to-zinc-50/40',
          accent: '#64748B',
          accentRgb: '100, 116, 139',
          light: 'rgba(100, 116, 139, 0.1)',
          dark: 'rgba(100, 116, 139, 0.9)',
          glow: '0 0 30px rgba(100, 116, 139, 0.3)',
          cardBg: 'from-slate-50/90 via-gray-50/70 to-white/80'
        }
    }
  }

  const theme = useMemo(() => getThemeColors(product.category || ''), [product.category])

  const handleBookmarkClick = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault()
    if (isBookmarkLoading) return
    
    setIsBookmarkLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      setIsBookmarked(!isBookmarked)
    } catch (error) {
      console.error('Failed to update bookmark:', error)
    } finally {
      setIsBookmarkLoading(false)
    }
  }, [isBookmarked, isBookmarkLoading])

  const handleLikeClick = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault()
    if (isLikeLoading) return
    
    setIsLikeLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      setIsLiked(!isLiked)
    } catch (error) {
      console.error('Failed to update like:', error)
    } finally {
      setIsLikeLoading(false)
    }
  }, [isLiked, isLikeLoading])

  const handleHoverStart = useCallback(() => setIsHovered(true), [])
  const handleHoverEnd = useCallback(() => setIsHovered(false), [])

  return (
    <motion.div
      className="group relative h-full"
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.15,
        type: "spring",
        stiffness: 100
      }}
      viewport={{ once: true }}
      whileHover={{
        y: -12,
        transition: { duration: 0.3, type: "spring", stiffness: 300 }
      }}
      onHoverStart={handleHoverStart}
      onHoverEnd={handleHoverEnd}
      role="article"
      aria-label={`${product.name} - ${product.description}`}
    >
      {/* 主卡片容器 */}
      <div className="relative h-full">
        {/* 背景光晕效果 */}
        <motion.div
          className="absolute -inset-1 rounded-[2rem] opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(135deg, ${theme.primary})`,
            filter: 'blur(20px)',
          }}
          animate={{
            scale: isHovered ? 1.02 : 1,
          }}
        />

        {/* 增强的主卡片 */}
        <div className={cn(
          "relative h-full rounded-3xl backdrop-blur-xl overflow-hidden",
          "border-2 transition-all duration-500",
          "shadow-lg group-hover:shadow-2xl",
          "bg-gradient-to-br"
        )}
        style={{
          background: `linear-gradient(135deg, ${theme.cardBg})`,
          borderColor: isHovered ? theme.accent : 'rgba(255, 255, 255, 0.3)',
          boxShadow: isHovered ? theme.glow : '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}>

          {/* 增强的顶部渐变条 */}
          <div
            className="absolute top-0 left-0 right-0 h-2 opacity-90"
            style={{ background: `linear-gradient(90deg, ${theme.primary})` }}
          />

          {/* 增强的背景装饰 */}
          <div className="absolute inset-0 opacity-[0.04] group-hover:opacity-[0.08] transition-opacity duration-500">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 2px 2px, ${theme.accent} 1px, transparent 0)`,
              backgroundSize: '24px 24px'
            }} />
          </div>

          {/* 动态背景光效 */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
            style={{
              background: `radial-gradient(circle at 50% 0%, ${theme.light}, transparent 70%)`
            }}
            animate={{
              background: [
                `radial-gradient(circle at 50% 0%, ${theme.light}, transparent 70%)`,
                `radial-gradient(circle at 100% 50%, ${theme.light}, transparent 70%)`,
                `radial-gradient(circle at 50% 100%, ${theme.light}, transparent 70%)`,
                `radial-gradient(circle at 0% 50%, ${theme.light}, transparent 70%)`,
                `radial-gradient(circle at 50% 0%, ${theme.light}, transparent 70%)`
              ]
            }}
            transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
          />

          {/* 浮动装饰元素 */}
          <div className="absolute top-6 right-6 w-20 h-20 opacity-5 group-hover:opacity-10 transition-opacity duration-500">
            <motion.div
              className="w-full h-full rounded-full"
              style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
              animate={{
                rotate: 360,
                scale: [1, 1.1, 1],
              }}
              transition={{
                rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
              }}
            />
          </div>

          {/* 高亮标签 */}
          {product.highlight && (
            <motion.div
              className="absolute -top-3 -right-3 z-20"
              initial={{ scale: 0, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            >
              <div className={cn(
                "px-4 py-2 rounded-full text-xs font-bold shadow-lg",
                "relative overflow-hidden"
              )}
              style={{ background: `linear-gradient(135deg, ${theme.primary})` }}>
                <motion.div
                  className="absolute inset-0"
                  style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                  animate={{
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <span className="relative z-10 flex items-center gap-1">
                  <Sparkles className="h-3 w-3" />
                  {tHighlights(product.highlight) || product.highlight}
                </span>
              </div>
            </motion.div>
          )}

          {/* 卡片内容 */}
          <div className="relative p-4 sm:p-6 lg:p-8 flex flex-col h-full">
            {/* 头部区域 */}
            <div className="flex items-start justify-between mb-4 sm:mb-6 lg:mb-8">
              {/* 主图标 */}
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="relative">
                  <motion.div
                    className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden"
                    style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                    whileHover={{ rotate: [0, -5, 5, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    {/* 图标光晕 */}
                    <motion.div
                      className="absolute inset-0 rounded-2xl"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      animate={{
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                    {renderIcon(product.iconName, "h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 text-white relative z-10")}
                  </motion.div>

                  {/* 外部光晕 */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    style={{
                      background: `linear-gradient(135deg, ${theme.primary})`,
                      filter: 'blur(16px)',
                      transform: 'scale(1.2)'
                    }}
                  />
                </div>
              </motion.div>

              {/* 增强的右上角操作按钮 */}
              <div className="flex items-center gap-3">
                {/* 增强的分类标签 */}
                {product.category && (
                  <motion.div
                    className="relative overflow-hidden rounded-2xl"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div
                      className="absolute inset-0 opacity-20"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                    />
                    <span
                      className="relative px-4 py-2 text-xs font-bold backdrop-blur-sm border border-white/40 rounded-2xl shadow-lg flex items-center gap-2"
                      style={{
                        color: theme.accent,
                        background: 'rgba(255, 255, 255, 0.8)'
                      }}
                    >
                      <motion.div
                        className="w-2 h-2 rounded-full"
                        style={{ background: theme.accent }}
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      {product.category}
                    </span>
                  </motion.div>
                )}

                {/* 增强的收藏按钮 */}
                <motion.button
                  className={cn(
                    "relative p-3 rounded-2xl backdrop-blur-sm border border-white/40 shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2",
                    isBookmarkLoading && "opacity-50 cursor-not-allowed",
                    isBookmarked ? "bg-white/90" : "bg-white/70 hover:bg-white/90"
                  )}
                  style={{
                    focusRingColor: theme.accent
                  }}
                  whileHover={!isBookmarkLoading ? { scale: 1.1, rotate: 5 } : {}}
                  whileTap={!isBookmarkLoading ? { scale: 0.95 } : {}}
                  onClick={handleBookmarkClick}
                  aria-label={isBookmarked ? tCard('removeBookmark') : tCard('addBookmark')}
                  aria-pressed={isBookmarked}
                  disabled={isBookmarkLoading}
                >
                  {/* 按钮背景光效 */}
                  {isBookmarked && (
                    <motion.div
                      className="absolute inset-0 rounded-2xl opacity-20"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      animate={{ opacity: [0.2, 0.4, 0.2] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}

                  {isBookmarkLoading ? (
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-slate-400 border-t-transparent" />
                  ) : (
                    <Bookmark
                      className={cn(
                        "h-5 w-5 transition-colors relative z-10",
                        isBookmarked ? "fill-current" : ""
                      )}
                      style={{ color: theme.accent }}
                    />
                  )}
                </motion.button>
              </div>
            </div>

            {/* 标题和描述 */}
            <div className="mb-4 sm:mb-6 lg:mb-8">
              <motion.h3
                className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-800 mb-2 sm:mb-3 lg:mb-4 leading-tight"
                style={{
                  background: `linear-gradient(135deg, #1e293b, ${theme.accent})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 }
                }}
              >
                {product.name}
              </motion.h3>
              <p className="text-slate-600 leading-relaxed line-clamp-3 text-xs sm:text-sm">
                {product.description}
              </p>
            </div>

            {/* 增强的特性展示 */}
            <div className="mb-4 sm:mb-6 lg:mb-8 flex-grow">
              <div className="space-y-4">
                {product.features.slice(0, 3).map((feature, featureIndex) => (
                  <motion.div
                    key={feature.text}
                    className="group/feature relative"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.4,
                      delay: featureIndex * 0.1 + 0.2,
                      type: "spring",
                      stiffness: 100
                    }}
                    viewport={{ once: true }}
                    whileHover={{ x: 6, scale: 1.02 }}
                  >
                    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-white/60 to-white/40 backdrop-blur-sm border border-white/40 hover:from-white/80 hover:to-white/60 transition-all duration-300 shadow-sm hover:shadow-md">
                      {/* 特性背景光效 */}
                      <motion.div
                        className="absolute inset-0 opacity-0 group-hover/feature:opacity-100 transition-opacity duration-300"
                        style={{
                          background: `linear-gradient(90deg, ${theme.light}, transparent)`
                        }}
                      />

                      <div className="relative flex items-center gap-3 sm:gap-4 p-3 sm:p-4">
                        {/* 增强的特性图标 */}
                        <motion.div
                          className="relative w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg overflow-hidden"
                          style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                          whileHover={{ scale: 1.1, rotate: 10 }}
                          transition={{ duration: 0.3 }}
                        >
                          {/* 图标内部光效 */}
                          <motion.div
                            className="absolute inset-0 rounded-xl"
                            style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                            animate={{ opacity: [0.8, 1, 0.8] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                          {renderIcon(feature.iconName, "h-4 w-4 sm:h-5 sm:w-5 text-white relative z-10")}
                        </motion.div>

                        {/* 增强的特性文本 */}
                        <div className="flex-1 min-w-0">
                          <span className="text-sm sm:text-base font-semibold text-slate-800 group-hover/feature:text-slate-900 transition-colors block leading-tight">
                            {feature.text}
                          </span>
                        </div>

                        {/* 增强的箭头指示器 */}
                        <motion.div
                          className="opacity-0 group-hover/feature:opacity-100 transition-all duration-300"
                          initial={{ x: -10, scale: 0.8 }}
                          whileHover={{ x: 0, scale: 1 }}
                        >
                          <div
                            className="w-6 h-6 rounded-lg flex items-center justify-center"
                            style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                          >
                            <ChevronRight className="h-3 w-3 text-white" />
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* 增强的统计信息卡片 */}
            <motion.div
              className="mb-4 sm:mb-5 lg:mb-6 p-4 sm:p-5 rounded-2xl sm:rounded-3xl bg-gradient-to-r from-white/70 to-white/50 backdrop-blur-sm border border-white/50 shadow-lg relative overflow-hidden"
              whileHover={{ scale: 1.02, y: -2 }}
              transition={{ duration: 0.3 }}
            >
              {/* 统计卡片背景光效 */}
              <motion.div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                style={{
                  background: `radial-gradient(circle at center, ${theme.light}, transparent 70%)`
                }}
              />

              <div className="relative grid grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                <div className="text-center">
                  <motion.div
                    className="text-base sm:text-lg lg:text-xl font-bold mb-2 flex items-center justify-center gap-1"
                    style={{ color: theme.accent }}
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                    99%
                  </motion.div>
                  <div className="text-xs sm:text-sm text-slate-600 font-medium">{tCard('satisfaction')}</div>
                </div>
                <div className="text-center border-x border-white/40">
                  <motion.div
                    className="text-base sm:text-lg lg:text-xl font-bold mb-2 flex items-center justify-center gap-1"
                    style={{ color: theme.accent }}
                    whileHover={{ scale: 1.1 }}
                  >
                    <Clock className="h-4 w-4 sm:h-5 sm:w-5" />
                    24/7
                  </motion.div>
                  <div className="text-xs sm:text-sm text-slate-600 font-medium">{tCard('support')}</div>
                </div>
                <div className="text-center">
                  <motion.div
                    className="text-base sm:text-lg lg:text-xl font-bold mb-2 flex items-center justify-center gap-1"
                    style={{ color: theme.accent }}
                    whileHover={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                    4.9
                  </motion.div>
                  <div className="text-xs sm:text-sm text-slate-600 font-medium">{tCard('rating')}</div>
                </div>
              </div>
            </motion.div>

            {/* 增强的价格信息 */}
            {product.price && (
              <motion.div
                className="mb-4 sm:mb-5 lg:mb-6 p-4 sm:p-5 rounded-2xl sm:rounded-3xl bg-gradient-to-r from-white/80 to-white/60 backdrop-blur-sm border border-white/50 shadow-lg relative overflow-hidden"
                whileHover={{ scale: 1.02, y: -2 }}
                transition={{ duration: 0.3 }}
              >
                {/* 价格卡片背景光效 */}
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  style={{
                    background: `linear-gradient(45deg, ${theme.light}, transparent 60%)`
                  }}
                />

                <div className="relative flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-xs sm:text-sm text-slate-600 mb-2 flex items-center gap-2 font-medium">
                      <motion.div
                        className="w-2 h-2 rounded-full"
                        style={{ background: theme.accent }}
                        animate={{ scale: [1, 1.3, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      {tCard('startingPrice')}
                    </div>
                    <motion.div
                      className="text-xl sm:text-2xl lg:text-3xl font-bold"
                      style={{ color: theme.accent }}
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      {product.price}
                    </motion.div>
                  </div>
                  <motion.div
                    className="p-3 sm:p-4 rounded-2xl shadow-lg relative overflow-hidden"
                    style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                    whileHover={{ scale: 1.1, rotate: 10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <motion.div
                      className="absolute inset-0 rounded-2xl"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      animate={{ opacity: [0.8, 1, 0.8] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                    <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-white relative z-10" />
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* 增强的底部操作区 */}
            <div className="flex items-center gap-3 sm:gap-4">
              {/* 增强的喜欢按钮 */}
              <motion.button
                className={cn(
                  "relative flex items-center justify-center p-3 sm:p-4 rounded-2xl backdrop-blur-sm border border-white/50 shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 overflow-hidden",
                  isLikeLoading && "opacity-50 cursor-not-allowed",
                  isLiked ? "bg-white/90" : "bg-white/70 hover:bg-white/90"
                )}
                style={{
                  '--focus-ring-color': theme.accent
                } as React.CSSProperties}
                whileHover={!isLikeLoading ? { scale: 1.05, rotate: 5 } : {}}
                whileTap={!isLikeLoading ? { scale: 0.95 } : {}}
                onClick={handleLikeClick}
                aria-label={isLiked ? tCard('removeLike') : tCard('addLike')}
                aria-pressed={isLiked}
                disabled={isLikeLoading}
              >
                {/* 喜欢按钮背景光效 */}
                {isLiked && (
                  <motion.div
                    className="absolute inset-0 rounded-2xl"
                    style={{ background: `linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2))` }}
                    animate={{ opacity: [0.2, 0.4, 0.2] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                )}

                {isLikeLoading ? (
                  <div className="h-5 w-5 sm:h-6 sm:w-6 animate-spin rounded-full border-2 border-slate-400 border-t-transparent" />
                ) : (
                  <Heart
                    className={cn(
                      "h-5 w-5 sm:h-6 sm:w-6 transition-colors relative z-10",
                      isLiked ? "fill-red-500 text-red-500" : "text-slate-500"
                    )}
                  />
                )}
              </motion.button>

              {/* 增强的主要按钮 */}
              <motion.div
                className="flex-1"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  asChild
                  className="w-full h-12 sm:h-13 lg:h-14 rounded-2xl font-bold text-white shadow-xl border-0 relative overflow-hidden group/btn text-sm sm:text-base lg:text-lg"
                  style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                >
                  <Link href={`/products/${product.slug}`} className="flex items-center justify-center gap-3">
                    {/* 按钮背景动画 */}
                    <motion.div
                      className="absolute inset-0"
                      style={{ background: `linear-gradient(135deg, ${theme.primary})` }}
                      whileHover={{
                        background: `linear-gradient(135deg, ${theme.primary.replace('500', '600').replace('600', '700')})`
                      }}
                      transition={{ duration: 0.3 }}
                    />

                    {/* 按钮光效 */}
                    <motion.div
                      className="absolute inset-0 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"
                      style={{
                        background: `linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)`
                      }}
                      animate={{
                        x: ['-100%', '100%']
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatDelay: 2
                      }}
                    />

                    <span className="relative z-10 font-semibold">{tCard('learnMore')}</span>
                    <motion.div
                      className="relative z-10"
                      animate={{ x: [0, 6, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6" />
                    </motion.div>
                  </Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
