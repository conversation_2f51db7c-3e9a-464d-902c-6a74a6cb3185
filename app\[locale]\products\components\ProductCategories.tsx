'use client'

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from '@/hooks/useTranslations'
import {
  Layers,
  Brain,
  Cloud,
  GraduationCap,
  Code,
  Sparkles,
  TrendingUp,
  Zap
} from "lucide-react"

interface Category {
  id: string
  name: string
  count: number
}

interface ProductCategoriesProps {
  categories: Category[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
}

// Category icons mapping
const categoryIcons = {
  'all': Layers,
  'AI服务': Brain,
  '云计算': Cloud,
  '教育科技': GraduationCap,
  '定制开发': Code
}

// Category descriptions mapping - will be replaced by translations
const getCategoryDescription = (categoryId: string, t: any) => {
  const descriptionMap: Record<string, string> = {
    'all': t('categories.descriptions.all'),
    'AI服务': t('categories.descriptions.ai'),
    '云计算': t('categories.descriptions.cloud'),
    '教育科技': t('categories.descriptions.education'),
    '定制开发': t('categories.descriptions.development')
  }
  return descriptionMap[categoryId] || ''
}

export function ProductCategories({ categories, selectedCategory, onCategoryChange }: ProductCategoriesProps) {
  const t = useTranslations('productsPage')

  return (
    <section className="py-20 border-b border-slate-100/50 bg-gradient-to-br from-slate-50/50 via-white to-blue-50/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-50 border border-blue-100 mb-6">
            <Sparkles className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">产品分类</span>
          </div>
          <h2 className="text-4xl font-bold text-slate-900 mb-4 text-gradient-modern">
            {t('categories.title')}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
            {t('categories.subtitle')}
          </p>
        </motion.div>

        {/* Categories Grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-12"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category, index) => {
            const IconComponent = categoryIcons[category.id as keyof typeof categoryIcons] || Layers
            const isSelected = selectedCategory === category.id
            const description = getCategoryDescription(category.id, t)

            return (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ y: -8, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="group"
              >
                <button
                  onClick={() => onCategoryChange(category.id)}
                  className={`
                    relative w-full p-6 rounded-2xl border-2 transition-all duration-500 overflow-hidden
                    ${isSelected
                      ? 'border-blue-500 bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-xl shadow-blue-500/25'
                      : 'border-slate-200 bg-white/80 backdrop-blur-sm text-slate-700 hover:border-blue-300 hover:bg-white hover:shadow-lg hover:shadow-blue-500/10'
                    }
                  `}
                >
                  {/* Background Pattern */}
                  <div className={`
                    absolute inset-0 opacity-5 transition-opacity duration-500
                    ${isSelected ? 'opacity-10' : 'group-hover:opacity-10'}
                  `}>
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600" />
                    <div className="absolute inset-0 grid-bg" />
                  </div>

                  {/* Content */}
                  <div className="relative z-10 text-center">
                    {/* Icon */}
                    <div className={`
                      inline-flex items-center justify-center w-14 h-14 rounded-xl mb-4 transition-all duration-500
                      ${isSelected
                        ? 'bg-white/20 text-white'
                        : 'bg-blue-50 text-blue-600 group-hover:bg-blue-100 group-hover:scale-110'
                      }
                    `}>
                      <IconComponent className="w-7 h-7" />
                    </div>

                    {/* Category Name */}
                    <h3 className={`
                      font-semibold text-lg mb-2 transition-colors duration-300
                      ${isSelected ? 'text-white' : 'text-slate-800 group-hover:text-slate-900'}
                    `}>
                      {category.name}
                    </h3>

                    {/* Description */}
                    <p className={`
                      text-sm mb-3 transition-colors duration-300 leading-relaxed
                      ${isSelected ? 'text-white/80' : 'text-slate-500 group-hover:text-slate-600'}
                    `}>
                      {description}
                    </p>

                    {/* Count Badge */}
                    <div className="flex items-center justify-center gap-2">
                      <Badge
                        variant="secondary"
                        className={`
                          text-xs font-medium px-3 py-1 transition-all duration-300
                          ${isSelected
                            ? 'bg-white/20 text-white border-white/30 hover:bg-white/30'
                            : 'bg-blue-50 text-blue-700 border-blue-200 group-hover:bg-blue-100'
                          }
                        `}
                      >
                        <TrendingUp className="w-3 h-3 mr-1" />
                        {category.count} 个产品
                      </Badge>
                    </div>
                  </div>

                  {/* Selection Indicator */}
                  {isSelected && (
                    <motion.div
                      className="absolute top-4 right-4"
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ type: "spring", stiffness: 200, damping: 15 }}
                    >
                      <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                        <Zap className="w-3 h-3 text-white" />
                      </div>
                    </motion.div>
                  )}

                  {/* Hover Glow Effect */}
                  <div className={`
                    absolute inset-0 rounded-2xl transition-opacity duration-500 pointer-events-none
                    ${isSelected
                      ? 'opacity-100 bg-gradient-to-br from-blue-400/20 to-blue-600/20'
                      : 'opacity-0 group-hover:opacity-100 bg-gradient-to-br from-blue-400/10 to-blue-600/10'
                    }
                  `} />
                </button>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Selected Category Info */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {selectedCategory !== 'all' && (
            <motion.div
              className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 shadow-sm"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 20 }}
            >
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 animate-pulse" />
                <span className="text-sm font-medium text-slate-700">
                  当前显示
                </span>
              </div>
              <div className="flex items-center gap-2 px-3 py-1 rounded-lg bg-white/60 border border-blue-200">
                <span className="font-semibold text-blue-700">
                  {categories.find(c => c.id === selectedCategory)?.name}
                </span>
                <span className="text-xs text-slate-500">分类</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-sm text-slate-600">共</span>
                <span className="font-bold text-blue-600 text-lg">
                  {categories.find(c => c.id === selectedCategory)?.count}
                </span>
                <span className="text-sm text-slate-600">个产品</span>
              </div>
            </motion.div>
          )}

          {selectedCategory === 'all' && (
            <motion.div
              className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-gradient-to-r from-slate-50 to-blue-50 border border-slate-200 shadow-sm"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 20 }}
            >
              <Layers className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-slate-700">
                显示所有产品分类
              </span>
              <div className="flex items-center gap-1">
                <span className="text-sm text-slate-600">共</span>
                <span className="font-bold text-blue-600 text-lg">
                  {categories.find(c => c.id === 'all')?.count}
                </span>
                <span className="text-sm text-slate-600">个产品</span>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-100 rounded-full opacity-20 animate-float" />
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-indigo-100 rounded-full opacity-30 animate-float" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-blue-200 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }} />
      </div>
    </section>
  )
}
